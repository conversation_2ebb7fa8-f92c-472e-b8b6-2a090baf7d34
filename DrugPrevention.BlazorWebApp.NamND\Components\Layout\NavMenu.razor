﻿@inject IJSRuntime JSRuntime

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid d-flex justify-content-between align-items-center">
        <a class="navbar-brand" href="">🛡️ Drug Prevention System</a>
        <div class="d-flex align-items-center">
            <span class="text-light me-3">Welcome, @currentUser</span>
            <a href="/Account/Logout" class="btn btn-outline-light btn-sm">
                <i class="bi bi-box-arrow-right"></i> Logout
            </a>
        </div>
    </div>
</div>

<input type="checkbox" title="Navigation menu" class="navbar-toggler" />

<div class="nav-scrollable" onclick="document.querySelector('.navbar-toggler').click()">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="/Home" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Home
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="/SurveyQuestionsNamNDs/SurveyQuestionsNamNDList" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> SurveyQuestionsNamNDList
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="counter">
                <span class="bi bi-plus-square-fill-nav-menu" aria-hidden="true"></span> Counter
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="weather">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> Weather
            </NavLink>
        </div>
    </nav>
</div>

@code {
    private string currentUser = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        // Get current user from cookie or authentication service
        currentUser = await JSRuntime.InvokeAsync<string>("eval", "document.cookie.split(';').find(row => row.startsWith('UserName='))?.split('=')[1] || 'User'");
        if (string.IsNullOrEmpty(currentUser) || currentUser == "undefined")
        {
            currentUser = "User";
        }
    }
}

