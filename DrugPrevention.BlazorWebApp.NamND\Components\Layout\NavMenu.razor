﻿<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">Drug Prevention System</a>
        <AuthorizeView>
            <Authorized>
                <span class="navbar-text me-3">Welcome, @context.User.Identity?.Name</span>
                <a href="/Account/Logout" class="btn btn-outline-light btn-sm">Logout</a>
            </Authorized>
            <NotAuthorized>
                <a href="/Account/Login" class="btn btn-outline-light btn-sm">Login</a>
            </NotAuthorized>
        </AuthorizeView>
    </div>
</div>

<input type="checkbox" title="Navigation menu" class="navbar-toggler" />

<div class="nav-scrollable" onclick="document.querySelector('.navbar-toggler').click()">
    <nav class="flex-column">
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Home
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="/SurveyQuestionsNamNDs/SurveyQuestionsNamNDList" Match="NavLinkMatch.All">
                <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> SurveyQuestionsNamNDList
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="counter">
                <span class="bi bi-plus-square-fill-nav-menu" aria-hidden="true"></span> Counter
            </NavLink>
        </div>

        <div class="nav-item px-3">
            <NavLink class="nav-link" href="weather">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> Weather
            </NavLink>
        </div>
    </nav>
</div>

