{"Version": 1, "Hash": "Cus2YJ5fFMeCzR8ocCA+qHTYjnApuAyCJTLis2roKy4=", "Source": "DrugPrevention.BlazorWebApp.NamND", "BasePath": "_content/DrugPrevention.BlazorWebApp.NamND", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "DrugPrevention.BlazorWebApp.NamND\\wwwroot", "Source": "DrugPrevention.BlazorWebApp.NamND", "ContentRoot": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\", "BasePath": "_content/DrugPrevention.BlazorWebApp.NamND", "Pattern": "**"}], "Assets": [{"Identity": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DrugPrevention.BlazorWebApp.NamND.styles.css", "SourceId": "DrugPrevention.BlazorWebApp.NamND", "SourceType": "Computed", "ContentRoot": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/DrugPrevention.BlazorWebApp.NamND", "RelativePath": "DrugPrevention.BlazorWebApp.NamND#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "umlcdq5meo", "Integrity": "hp2tyufFUAVpKmdx1VWUulMu1kNasQNsBtLICfMpVfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DrugPrevention.BlazorWebApp.NamND.styles.css", "FileLength": 5949, "LastWriteTime": "2025-06-19T06:11:36+00:00"}, {"Identity": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DrugPrevention.BlazorWebApp.NamND.bundle.scp.css", "SourceId": "DrugPrevention.BlazorWebApp.NamND", "SourceType": "Computed", "ContentRoot": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/DrugPrevention.BlazorWebApp.NamND", "RelativePath": "DrugPrevention.BlazorWebApp.NamND#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "umlcdq5meo", "Integrity": "hp2tyufFUAVpKmdx1VWUulMu1kNasQNsBtLICfMpVfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DrugPrevention.BlazorWebApp.NamND.bundle.scp.css", "FileLength": 5949, "LastWriteTime": "2025-06-19T06:11:36+00:00"}, {"Identity": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\app.css", "SourceId": "DrugPrevention.BlazorWebApp.NamND", "SourceType": "Discovered", "ContentRoot": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\", "BasePath": "_content/DrugPrevention.BlazorWebApp.NamND", "RelativePath": "app#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "da95v2qkru", "Integrity": "u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css", "FileLength": 2591, "LastWriteTime": "2025-06-12T08:52:06+00:00"}, {"Identity": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\bootstrap\\bootstrap.min.css", "SourceId": "DrugPrevention.BlazorWebApp.NamND", "SourceType": "Discovered", "ContentRoot": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\", "BasePath": "_content/DrugPrevention.BlazorWebApp.NamND", "RelativePath": "bootstrap/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-12T08:52:06+00:00"}, {"Identity": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\bootstrap\\bootstrap.min.css.map", "SourceId": "DrugPrevention.BlazorWebApp.NamND", "SourceType": "Discovered", "ContentRoot": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\", "BasePath": "_content/DrugPrevention.BlazorWebApp.NamND", "RelativePath": "bootstrap/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-12T08:52:06+00:00"}, {"Identity": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\favicon.png", "SourceId": "DrugPrevention.BlazorWebApp.NamND", "SourceType": "Discovered", "ContentRoot": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\", "BasePath": "_content/DrugPrevention.BlazorWebApp.NamND", "RelativePath": "favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ifv42okdf2", "Integrity": "4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png", "FileLength": 1148, "LastWriteTime": "2025-06-12T08:52:06+00:00"}, {"Identity": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\hamster.gif", "SourceId": "DrugPrevention.BlazorWebApp.NamND", "SourceType": "Discovered", "ContentRoot": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\", "BasePath": "_content/DrugPrevention.BlazorWebApp.NamND", "RelativePath": "hamster#[.{fingerprint}]?.gif", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "a0vl44ky7a", "Integrity": "OmShwrq3kntKhEPDLf3xFP7/oF+D5jg90oJ6BsbkmNU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\hamster.gif", "FileLength": 60556, "LastWriteTime": "2025-06-12T19:26:55+00:00"}, {"Identity": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\pacman.gif", "SourceId": "DrugPrevention.BlazorWebApp.NamND", "SourceType": "Discovered", "ContentRoot": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\", "BasePath": "_content/DrugPrevention.BlazorWebApp.NamND", "RelativePath": "pacman#[.{fingerprint}]?.gif", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aug6tgiv90", "Integrity": "PgjYJ/iGYa+qTpVeVtpyNDqBwvQeoq86zZ8Ppjga4fY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\pacman.gif", "FileLength": 54276, "LastWriteTime": "2025-06-12T19:34:28+00:00"}], "Endpoints": [{"Route": "app.css", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "app.da95v2qkru.css", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "da95v2qkru"}, {"Name": "label", "Value": "app.css"}, {"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\bootstrap\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\bootstrap\\bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "DrugPrevention.BlazorWebApp.NamND.bundle.scp.css", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DrugPrevention.BlazorWebApp.NamND.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hp2tyufFUAVpKmdx1VWUulMu1kNasQNsBtLICfMpVfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Jun 2025 06:11:36 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hp2tyufFUAVpKmdx1VWUulMu1kNasQNsBtLICfMpVfY="}]}, {"Route": "DrugPrevention.BlazorWebApp.NamND.styles.css", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DrugPrevention.BlazorWebApp.NamND.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hp2tyufFUAVpKmdx1VWUulMu1kNasQNsBtLICfMpVfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Jun 2025 06:11:36 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hp2tyufFUAVpKmdx1VWUulMu1kNasQNsBtLICfMpVfY="}]}, {"Route": "DrugPrevention.BlazorWebApp.NamND.umlcdq5meo.bundle.scp.css", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\DrugPrevention.BlazorWebApp.NamND.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hp2tyufFUAVpKmdx1VWUulMu1kNasQNsBtLICfMpVfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Jun 2025 06:11:36 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "umlcdq5meo"}, {"Name": "label", "Value": "DrugPrevention.BlazorWebApp.NamND.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-hp2tyufFUAVpKmdx1VWUulMu1kNasQNsBtLICfMpVfY="}]}, {"Route": "DrugPrevention.BlazorWebApp.NamND.umlcdq5meo.styles.css", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\obj\\Debug\\net8.0\\scopedcss\\bundle\\DrugPrevention.BlazorWebApp.NamND.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5949"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"hp2tyufFUAVpKmdx1VWUulMu1kNasQNsBtLICfMpVfY=\""}, {"Name": "Last-Modified", "Value": "Thu, 19 Jun 2025 06:11:36 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "umlcdq5meo"}, {"Name": "label", "Value": "DrugPrevention.BlazorWebApp.NamND.styles.css"}, {"Name": "integrity", "Value": "sha256-hp2tyufFUAVpKmdx1VWUulMu1kNasQNsBtLICfMpVfY="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "label", "Value": "favicon.png"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "favicon.png", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "hamster.a0vl44ky7a.gif", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\hamster.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60556"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"OmShwrq3kntKhEPDLf3xFP7/oF+D5jg90oJ6BsbkmNU=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 19:26:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a0vl44ky7a"}, {"Name": "label", "Value": "hamster.gif"}, {"Name": "integrity", "Value": "sha256-OmShwrq3kntKhEPDLf3xFP7/oF+D5jg90oJ6BsbkmNU="}]}, {"Route": "hamster.gif", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\hamster.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60556"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"OmShwrq3kntKhEPDLf3xFP7/oF+D5jg90oJ6BsbkmNU=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 19:26:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OmShwrq3kntKhEPDLf3xFP7/oF+D5jg90oJ6BsbkmNU="}]}, {"Route": "pacman.aug6tgiv90.gif", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\pacman.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54276"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"PgjYJ/iGYa+qTpVeVtpyNDqBwvQeoq86zZ8Ppjga4fY=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 19:34:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aug6tgiv90"}, {"Name": "label", "Value": "pacman.gif"}, {"Name": "integrity", "Value": "sha256-PgjYJ/iGYa+qTpVeVtpyNDqBwvQeoq86zZ8Ppjga4fY="}]}, {"Route": "pacman.gif", "AssetFile": "D:\\FPTU\\FPTU\\VII_SE1709\\PRN222\\Project\\2\\SU25_PRN222_SE1709_ASM2_SE183115_NamND\\DrugPrevention.BlazorWebApp.NamND\\wwwroot\\pacman.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54276"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"PgjYJ/iGYa+qTpVeVtpyNDqBwvQeoq86zZ8Ppjga4fY=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 19:34:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PgjYJ/iGYa+qTpVeVtpyNDqBwvQeoq86zZ8Ppjga4fY="}]}]}