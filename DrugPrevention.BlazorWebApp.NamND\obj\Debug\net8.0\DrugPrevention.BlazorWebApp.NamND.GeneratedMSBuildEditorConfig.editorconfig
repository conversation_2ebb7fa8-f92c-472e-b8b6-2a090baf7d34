is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = DrugPrevention.BlazorWebApp.NamND
build_property.RootNamespace = DrugPrevention.BlazorWebApp.NamND
build_property.ProjectDir = D:\FPTU\FPTU\VII_SE1709\PRN222\Project\2\SU25_PRN222_SE1709_ASM2_SE183115_NamND\DrugPrevention.BlazorWebApp.NamND\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = D:\FPTU\FPTU\VII_SE1709\PRN222\Project\2\SU25_PRN222_SE1709_ASM2_SE183115_NamND\DrugPrevention.BlazorWebApp.NamND
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Account/AuthenticationWrapper.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBY2NvdW50XEF1dGhlbnRpY2F0aW9uV3JhcHBlci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Account/Login.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBY2NvdW50XExvZ2luLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Account/Logout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBY2NvdW50XExvZ291dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/App.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xBcHAucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Pages/Counter.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xDb3VudGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Pages/Error.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xFcnJvci5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Pages/Home.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xIb21lLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Pages/Index.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xJbmRleC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Pages/Weather.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xQYWdlc1xXZWF0aGVyLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Routes.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xSb3V0ZXMucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/SurveyQuestionsNamNDs/SurveyQuestionsNamNDDetail.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTdXJ2ZXlRdWVzdGlvbnNOYW1ORHNcU3VydmV5UXVlc3Rpb25zTmFtTkREZXRhaWwucmF6b3I=
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/SurveyQuestionsNamNDs/SurveyQuestionsNamNDForm.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTdXJ2ZXlRdWVzdGlvbnNOYW1ORHNcU3VydmV5UXVlc3Rpb25zTmFtTkRGb3JtLnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/SurveyQuestionsNamNDs/SurveyQuestionsNamNDList.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xTdXJ2ZXlRdWVzdGlvbnNOYW1ORHNcU3VydmV5UXVlc3Rpb25zTmFtTkRMaXN0LnJhem9y
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/_Imports.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xfSW1wb3J0cy5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = 

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Layout/MainLayout.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTWFpbkxheW91dC5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-yepyswd3kb

[D:/FPTU/FPTU/VII_SE1709/PRN222/Project/2/SU25_PRN222_SE1709_ASM2_SE183115_NamND/DrugPrevention.BlazorWebApp.NamND/Components/Layout/NavMenu.razor]
build_metadata.AdditionalFiles.TargetPath = Q29tcG9uZW50c1xMYXlvdXRcTmF2TWVudS5yYXpvcg==
build_metadata.AdditionalFiles.CssScope = b-dhznlo4bd4
