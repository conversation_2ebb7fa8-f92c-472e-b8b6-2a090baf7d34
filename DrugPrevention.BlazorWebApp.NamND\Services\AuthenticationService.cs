using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.JSInterop;
using System.Security.Claims;
using DrugPrevention.Services.NamND;
using DrugPrevention.Repositories.NamND.Models;

namespace DrugPrevention.BlazorWebApp.NamND.Services
{
    public interface IAuthenticationService
    {
        Task<bool> LoginAsync(string username, string password);
        Task LogoutAsync();
        Task<bool> IsAuthenticatedAsync();
        Task<string> GetCurrentUserAsync();
    }

    public class AuthenticationService : IAuthenticationService
    {
        private readonly IServiceProviders _serviceProviders;
        private readonly IJSRuntime _jsRuntime;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public AuthenticationService(IServiceProviders serviceProviders, IJSRuntime jsRuntime, IHttpContextAccessor httpContextAccessor)
        {
            _serviceProviders = serviceProviders;
            _jsRuntime = jsRuntime;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<bool> LoginAsync(string username, string password)
        {
            try
            {
                var userAccount = await _serviceProviders.System_UserAccountService.GetUserAccount(username, password);

                if (userAccount != null)
                {
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.Name, username),
                        new Claim(ClaimTypes.Role, userAccount.RoleId.ToString()),
                        new Claim("UserAccountId", userAccount.UserAccountID.ToString()),
                        new Claim("FullName", userAccount.FullName ?? string.Empty)
                    };

                    var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                    var principal = new ClaimsPrincipal(identity);

                    var httpContext = _httpContextAccessor.HttpContext;
                    if (httpContext != null)
                    {
                        await httpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal);
                        
                        // Set additional cookie for username
                        httpContext.Response.Cookies.Append("UserName", userAccount.UserName, new CookieOptions
                        {
                            HttpOnly = false,
                            Secure = false,
                            SameSite = SameSiteMode.Lax
                        });
                    }

                    return true;
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext != null)
            {
                await httpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                
                // Clear username cookie
                httpContext.Response.Cookies.Delete("UserName");
            }

            // Clear cookies via JavaScript as well
            await _jsRuntime.InvokeVoidAsync("eval", "document.cookie = 'UserName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'");
            await _jsRuntime.InvokeVoidAsync("eval", "document.cookie = '.AspNetCore.Cookies=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'");
        }

        public async Task<bool> IsAuthenticatedAsync()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            return httpContext?.User?.Identity?.IsAuthenticated ?? false;
        }

        public async Task<string> GetCurrentUserAsync()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            return httpContext?.User?.Identity?.Name ?? string.Empty;
        }
    }
}
