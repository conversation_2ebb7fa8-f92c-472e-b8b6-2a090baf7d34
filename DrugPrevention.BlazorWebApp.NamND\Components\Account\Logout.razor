@page "/Account/Logout"
@attribute [AllowAnonymous]
@using Microsoft.AspNetCore.Authentication.Cookies
@using Microsoft.AspNetCore.Authentication
@using DrugPrevention.BlazorWebApp.NamND.Services
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject AuthenticationStateProvider AuthenticationStateProvider
@rendermode InteractiveServer

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Logout - Drug Prevention System</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header Styling */
        .logout-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
        }

        .logout-navbar-brand {
            color: white !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .logout-navbar-brand::before {
            content: "🛡️";
            margin-right: 0.5rem;
            font-size: 1.8rem;
        }

        /* Main Content */
        .logout-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .logout-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .logout-title {
            color: #333;
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .logout-message {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        .logout-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1.5rem;
        }

        .btn-login-again {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .btn-login-again:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
            text-decoration: none;
        }

        /* Floating Animation */
        .logout-card {
            animation: float 6s ease-in-out infinite;
        }

        @@keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Responsive Design */
        @@media (max-width: 768px) {
            .logout-card {
                padding: 2rem;
                margin: 1rem;
            }

            .logout-title {
                font-size: 1.5rem;
            }
        }

        /* Background Animation */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="a" cx=".5" cy=".5" r=".5"><stop offset="0" stop-color="%23ffffff" stop-opacity=".1"/><stop offset="1" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="20" cy="20" r="10" fill="url(%23a)"><animate attributeName="cy" values="20;80;20" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="80" r="15" fill="url(%23a)"><animate attributeName="cy" values="80;20;80" dur="4s" repeatCount="indefinite"/></circle></svg>') repeat;
            opacity: 0.1;
            z-index: -1;
        }
    </style>
</head>

<body>
    <header class="logout-header">
        <div class="container">
            <a class="logout-navbar-brand" href="/">Drug Prevention System</a>
        </div>
    </header>

    <div class="logout-container">
        <div class="logout-card">
            <div class="logout-icon">
                <i class="fas fa-sign-out-alt"></i>
            </div>
            <h1 class="logout-title">Logged Out</h1>
            <p class="logout-message">You have successfully logged out of the application.</p>
            
            <a href="/Account/Login" class="btn-login-again">
                <i class="fas fa-sign-in-alt me-2"></i>
                Login Again
            </a>
        </div>
    </div>
</body>
</html>

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Clear authentication state
            var customAuthStateProvider = (CustomAuthenticationStateProvider)AuthenticationStateProvider;
            await customAuthStateProvider.UpdateAuthenticationState(null);

            // Clear authentication cookie
            await JSRuntime.InvokeVoidAsync("eval", "document.cookie = 'UserName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'");

            // Clear any other authentication-related cookies
            await JSRuntime.InvokeVoidAsync("eval", "document.cookie.split(';').forEach(function(c) { document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/'); });");

            // Auto redirect to login after 3 seconds
            await Task.Delay(3000);
            Navigation.NavigateTo("/Account/Login", true);
        }
        catch (Exception)
        {
            // If there's an error, still redirect to login
            Navigation.NavigateTo("/Account/Login", true);
        }
    }
}
