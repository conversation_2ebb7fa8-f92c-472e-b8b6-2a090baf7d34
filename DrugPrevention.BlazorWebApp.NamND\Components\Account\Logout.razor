@page "/Account/Logout"
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer

<div class="logout-container">
    <div class="logout-card">
        <div class="logout-icon">
            <i class="fas fa-sign-out-alt"></i>
        </div>
        <h2>Logging Out...</h2>
        <p>You are being signed out of the system.</p>
        <div class="spinner">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
    </div>
</div>

<style>
    .logout-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: 'Poppins', sans-serif;
    }

    .logout-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 3rem;
        text-align: center;
        max-width: 400px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .logout-icon {
        font-size: 4rem;
        color: #667eea;
        margin-bottom: 1rem;
    }

    .logout-card h2 {
        color: #333;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .logout-card p {
        color: #666;
        margin-bottom: 2rem;
    }

    .spinner {
        font-size: 2rem;
        color: #667eea;
    }
</style>

@code {
    protected override async Task OnInitializedAsync()
    {
        await Task.Delay(1000); // Show logout message briefly
        
        await JSRuntime.InvokeVoidAsync("eval", @"
            fetch('/Account/SignOut', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            }).then(() => {
                document.cookie = 'UserName=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                window.location.href = '/Account/Login';
            });
        ");
    }
}
