﻿@using DrugPrevention.BlazorWebApp.NamND.Components.Account

<Router AppAssembly="typeof(Program).Assembly">
    <Found Context="routeData">
        @if (IsPublicRoute(routeData.PageType))
        {
            <RouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)" />
        }
        else
        {
            <AuthenticationWrapper>
                <RouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)" />
            </AuthenticationWrapper>
        }
        <FocusOnNavigate RouteData="routeData" Selector="h1" />
    </Found>
</Router>

@code {
    private bool IsPublicRoute(Type pageType)
    {
        // Define which routes are public (don't require authentication)
        var publicRoutes = new[]
        {
            typeof(Components.Account.Login),
            typeof(Components.Account.Logout),
            typeof(Components.Pages.Index),
            typeof(Components.Pages.Error)
        };

        return publicRoutes.Contains(pageType);
    }
}
