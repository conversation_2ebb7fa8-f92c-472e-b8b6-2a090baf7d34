[{"ContainingType": "DrugPrevention.BlazorWebApp.NamND.Components.Account.AccountController", "Method": "SignIn", "RelativePath": "Account/SignIn", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "DrugPrevention.BlazorWebApp.NamND.Components.Account.SignInRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "DrugPrevention.BlazorWebApp.NamND.Components.Account.AccountController", "Method": "SignOut", "RelativePath": "Account/SignOut", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}]