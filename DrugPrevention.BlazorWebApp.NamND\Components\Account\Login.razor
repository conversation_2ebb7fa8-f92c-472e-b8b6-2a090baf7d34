@page "/Account/Login"
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Authentication.Cookies
@using System.Security.Claims
@using DrugPrevention.Services.NamND
@inject IServiceProviders ServiceProviders
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Login - Drug Prevention System</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .login-header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
        }

        .login-navbar-brand {
            color: white !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .login-navbar-brand::before {
            content: "🛡️";
            margin-right: 0.5rem;
            font-size: 1.8rem;
        }

        .login-navbar-brand:hover {
            color: #ffd700 !important;
            transform: scale(1.02);
            transition: all 0.3s ease;
        }

        .login-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: float 6s ease-in-out infinite;
        }

        @@keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .login-title {
            text-align: center;
            color: #333;
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .login-subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 2rem;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-control {
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: #f8f9fa;
            width: 100%;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            background-color: white;
            outline: none;
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1.1rem;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 1rem;
            cursor: pointer;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .alert-error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .text-danger {
            color: #dc3545 !important;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        @@media (max-width: 768px) {
            .login-card {
                padding: 2rem;
                margin: 1rem;
            }

            .login-title {
                font-size: 1.5rem;
            }
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="a" cx=".5" cy=".5" r=".5"><stop offset="0" stop-color="%23ffffff" stop-opacity=".1"/><stop offset="1" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="20" cy="20" r="10" fill="url(%23a)"><animate attributeName="cy" values="20;80;20" dur="3s" repeatCount="indefinite"/></circle><circle cx="80" cy="80" r="15" fill="url(%23a)"><animate attributeName="cy" values="80;20;80" dur="4s" repeatCount="indefinite"/></circle></svg>') repeat;
            opacity: 0.1;
            z-index: -1;
        }
    </style>
</head>

<body>
    <header class="login-header">
        <div class="container">
            <a class="login-navbar-brand" href="/">Drug Prevention System</a>
        </div>
    </header>

    <div class="login-container">
        <div class="login-card">
            <h1 class="login-title">Welcome Back</h1>
            <p class="login-subtitle">Sign in to your account</p>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    @errorMessage
                </div>
            }

            <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin">
                <DataAnnotationsValidator />
                
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-user"></i>
                        Username
                    </label>
                    <InputText @bind-Value="loginModel.UserName" class="form-control" placeholder="Enter your username" />
                    <ValidationMessage For="@(() => loginModel.UserName)" class="text-danger" />
                </div>

                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-lock"></i>
                        Password
                    </label>
                    <InputText type="password" @bind-Value="loginModel.Password" class="form-control" placeholder="Enter your password" />
                    <ValidationMessage For="@(() => loginModel.Password)" class="text-danger" />
                </div>

                <button type="submit" class="btn btn-login" disabled="@isLoading">
                    @if (isLoading)
                    {
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        <span>Signing In...</span>
                    }
                    else
                    {
                        <i class="fas fa-sign-in-alt me-2"></i>
                        <span>Sign In</span>
                    }
                </button>
            </EditForm>
        </div>
    </div>
</body>
</html>

@code {
    private LoginViewModel loginModel = new();
    private string errorMessage = string.Empty;
    private bool isLoading = false;

    public class LoginViewModel
    {
        [Required(ErrorMessage = "Username is required")]
        public string UserName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;
    }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            StateHasChanged();

            var userAccount = await ServiceProviders.System_UserAccountService.GetUserAccount(loginModel.UserName, loginModel.Password);

            if (userAccount != null)
            {
                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.Name, loginModel.UserName),
                    new Claim(ClaimTypes.Role, userAccount.RoleId.ToString()),
                    new Claim("FullName", userAccount.FullName ?? ""),
                    new Claim("UserAccountID", userAccount.UserAccountID.ToString())
                };

                var identity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var principal = new ClaimsPrincipal(identity);

                // Set cookie and redirect using JavaScript
                await JSRuntime.InvokeVoidAsync("eval", $@"
                    document.cookie = 'UserName={userAccount.UserName}; path=/';
                    fetch('/Account/SignIn', {{
                        method: 'POST',
                        headers: {{
                            'Content-Type': 'application/json',
                        }},
                        body: JSON.stringify({{
                            userName: '{userAccount.UserName}',
                            roleId: '{userAccount.RoleId}',
                            fullName: '{userAccount.FullName ?? ""}',
                            userAccountId: '{userAccount.UserAccountID}'
                        }})
                    }}).then(() => {{
                        window.location.href = '/';
                    }});
                ");
            }
            else
            {
                errorMessage = "Login failed, please check your account";
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during login. Please try again.";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
