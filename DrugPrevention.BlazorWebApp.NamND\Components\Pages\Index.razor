@page "/"
@rendermode InteractiveServer
@using DrugPrevention.BlazorWebApp.NamND.Services
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation

@code {
    protected override async Task OnInitializedAsync()
    {
        var isAuthenticated = await AuthService.IsAuthenticatedAsync();
        if (isAuthenticated)
        {
            Navigation.NavigateTo("/Home", true);
        }
        else
        {
            Navigation.NavigateTo("/Account/Login", true);
        }
    }
}
