@using Microsoft.AspNetCore.Components.Authorization
@using DrugPrevention.BlazorWebApp.NamND.Services
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation

@if (isAuthenticated)
{
    @ChildContent
}
else
{
    <div class="d-flex justify-content-center align-items-center" style="min-height: 100vh;">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Checking authentication...</p>
        </div>
    </div>
}

@code {
    [Parameter] public RenderFragment ChildContent { get; set; }
    
    private bool isAuthenticated = false;

    protected override async Task OnInitializedAsync()
    {
        isAuthenticated = await AuthService.IsAuthenticatedAsync();
        
        if (!isAuthenticated)
        {
            Navigation.NavigateTo("/Account/Login", true);
        }
    }
}
