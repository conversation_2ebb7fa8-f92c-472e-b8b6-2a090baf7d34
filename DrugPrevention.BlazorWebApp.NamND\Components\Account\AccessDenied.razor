@page "/Account/AccessDenied"

<PageTitle>Access Denied</PageTitle>

<div class="access-denied-container">
    <div class="access-denied-card">
        <div class="access-denied-icon">
            <i class="fas fa-ban"></i>
        </div>
        <h2>Access Denied</h2>
        <p>You don't have permission to access this page.</p>
        <div class="access-denied-actions">
            <a href="/Account/Login" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i> Login
            </a>
            <a href="/" class="btn btn-secondary">
                <i class="fas fa-home"></i> Go Home
            </a>
        </div>
    </div>
</div>

<style>
    .access-denied-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: 'Poppin<PERSON>', sans-serif;
    }

    .access-denied-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        padding: 3rem;
        text-align: center;
        max-width: 400px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .access-denied-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 1rem;
    }

    .access-denied-card h2 {
        color: #333;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .access-denied-card p {
        color: #666;
        margin-bottom: 2rem;
    }

    .access-denied-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        border: none;
    }

    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
</style>
