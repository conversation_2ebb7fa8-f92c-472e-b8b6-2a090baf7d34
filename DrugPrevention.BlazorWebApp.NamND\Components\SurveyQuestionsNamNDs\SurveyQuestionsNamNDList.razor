﻿@page "/SurveyQuestionsNamNDs/SurveyQuestionsNamNDList"
@page "/SurveyQuestionsNamNDs"
@attribute [StreamRendering]
@attribute [Authorize]
@using DrugPrevention.Repositories.NamND.Models

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .stats-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        text-align: center;
    }

    .modern-table {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
    }

    .modern-table thead {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .modern-table thead th {
        color: white;
        font-weight: 700;
        padding: 1.5rem 1rem;
        border: none;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        text-align: center;
        vertical-align: middle;
        white-space: nowrap;
        line-height: 1.2;
    }

    .modern-table tbody tr {
        transition: all 0.3s ease;
        border-bottom: 1px solid #e2e8f0;
    }

    .modern-table tbody tr:hover {
        background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
        transform: scale(1.01);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .modern-table tbody tr:last-child {
        border-bottom: none;
    }

    .modern-table tbody td {
        padding: 1.2rem 1rem;
        vertical-align: middle;
        border: none;
        font-size: 0.85rem;
        text-align: center;
    }

    .question-text {
        font-weight: 600;
        color: #2d3748;
        max-width: 250px;
        word-wrap: break-word;
        text-align: left;
        line-height: 1.4;
        margin: 0;
    }

    .date-text {
        font-weight: 500;
        color: #4a5568;
        white-space: nowrap;
    }

    .order-badge {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 0.8rem;
        display: inline-block;
        box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .status-badge {
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-block;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .status-active {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(72, 187, 120, 0.3);
    }

    .status-inactive {
        background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(237, 137, 54, 0.3);
    }

    .required-yes {
        background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(56, 161, 105, 0.3);
    }

    .required-no {
        background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
        color: white;
        box-shadow: 0 2px 8px rgba(229, 62, 62, 0.3);
    }

    .question-type {
        background: linear-gradient(135deg, #805ad5 0%, #6b46c1 100%);
        color: white;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        display: inline-block;
        box-shadow: 0 2px 8px rgba(128, 90, 213, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .risk-weight {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 0.3rem 0.6rem;
        border-radius: 15px;
        font-weight: 600;
        font-size: 0.8rem;
        display: inline-block;
        box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .survey-name {
        color: #4a5568;
        font-style: italic;
        font-weight: 500;
    }

    .options-text {
        max-width: 200px;
        word-wrap: break-word;
        color: #718096;
        font-size: 0.85rem;
    }

    .btn-modern {
        padding: 0.4rem 0.8rem;
        border-radius: 20px;
        border: none;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 0.8rem;
        margin: 0 0.2rem;
        display: inline-block;
    }

    .btn-detail {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-detail:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-edit {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
    }

    .btn-edit:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(240, 147, 251, 0.4);
        color: white;
    }

    .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 70vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
    }

    .btn-create {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 25px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 2rem;
        font-size: 0.9rem;
    }

    .btn-create:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(72, 187, 120, 0.4);
        color: white;
    }

    /* Thêm style cho search box */
    .search-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .search-title {
        color: #4a5568;
        font-weight: 600;
        margin-bottom: 1rem;
        font-size: 1.1rem;
    }

    .search-input {
        border-radius: 10px;
        border: 1px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.25);
    }

    .btn-search {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-search:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .btn-reset {
        background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-reset:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(160, 174, 192, 0.4);
    }

    /* Style cho phân trang */
    .pagination {
        display: flex;
        justify-content: center;
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .pagination-item {
        margin: 0 0.25rem;
        padding: 0.5rem 1rem;
        border-radius: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        font-size: 0.9rem;
    }

    .pagination-nav {
        background: linear-gradient(135deg, #a0aec0 0%, #718096 100%);
        color: white;
        border: none;
    }

    .pagination-nav:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(160, 174, 192, 0.4);
    }

    .pagination-nav:disabled {
        background: #e2e8f0;
        color: #a0aec0;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .pagination-number {
        background: white;
        color: #4a5568;
        border: 1px solid #e2e8f0;
    }

    .pagination-number:hover {
        background: #f7fafc;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }

    .pagination-active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
    }

    .pagination-active:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .page-info {
        text-align: center;
        margin-top: 1rem;
        color: #4a5568;
        font-size: 0.9rem;
    }   
</style>

<div class="page-header text-center">
    <div class="container">
        <h1 class="mb-0">📋 Survey Questions List</h1>
        <p class="mb-0 mt-2 opacity-75">Manage and monitor survey questions</p>
    </div>
</div>

@if (surveyQuestionsNamNDs == null)
{
    <div class="loading-container">
        <div class="text-center">
            <img src="/pacman.gif" alt="Loading..." style="width: 300px; height: 300px;" />
        </div>
    </div>
}
else
{
    <div class="container">
        <!-- Search Box -->
        <div class="search-card">
            <h3 class="search-title">🔍 Search Questions</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="form-group mb-3">
                        <label for="questionId" class="form-label">Question ID</label>
                        <input type="number" id="questionId" class="form-control search-input" @bind="searchQuestionId" placeholder="Enter ID..." />
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group mb-3">
                        <label for="questionText" class="form-label">Question Text</label>
                        <input type="text" id="questionText" class="form-control search-input" @bind="searchQuestionText" placeholder="Enter text..." />
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group mb-3">
                        <label for="surveyName" class="form-label">Survey</label>
                        <select id="surveyName" class="form-control search-input" @bind="searchSurveyId">
                            <option value="0">-- All Surveys --</option>
                            @if (surveys != null)
                            {
                                @foreach (var survey in surveys)
                                {
                                    <option value="@survey.SurveyNamNDID">@survey.SurveyName</option>
                                }
                            }
                        </select>
                    </div>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <div class="form-group mb-3 w-100">
                        <button class="btn btn-search w-100" @onclick="SearchQuestions">Search</button>
                    </div>
                </div>
            </div>
            <div class="text-end">
                <button class="btn btn-reset" @onclick="ResetSearch">Reset</button>
            </div>
        </div>

        <div class="text-center">
            <a href="/SurveyQuestionsNamNDs/SurveyQuestionsNamNDForm" class="btn-create">
                ➕ Create New Question
            </a>
        </div>

        <div class="d-flex justify-content-between align-items-center mb-4">
            <div class="d-flex align-items-center">
                <span class="me-2">Show</span>
                <select class="form-select search-input" style="width: 80px;" @bind="pageSize" @bind:after="() => GoToPage(1)">
                    <option value="5">5</option>
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="50">50</option>
                </select>
                <span class="ms-2">entries</span>
            </div>
            
            <div class="stats-card" style="margin-bottom: 0; padding: 0.8rem 1.5rem;">
                <h3 class="mb-0" style="font-size: 1rem;">📊 Total Questions: @surveyQuestionsNamNDs?.Count</h3>
            </div>
        </div>

        <table class="table modern-table">
            <thead>
                <tr>
                    <th>Question<br/>Text</th>
                    <th>Order</th>
                    <th>Status</th>
                    <th>Created<br/>Date</th>
                    <th>Type</th>
                    <th>Required</th>
                    <th>Options</th>
                    <th>Risk<br/>Weight</th>
                    <th>Survey</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var surveyQuestionsNamND in pagedQuestions)
                {
                    <tr>
                        <td style="text-align: left;">
                            <div class="question-text">@surveyQuestionsNamND.QuestionText</div>
                        </td>
                        <td>
                            <span class="order-badge">@surveyQuestionsNamND.QuestionOrder</span>
                        </td>
                        <td>
                            <span class="status-badge @(surveyQuestionsNamND.IsActive ? "status-active" : "status-inactive")">
                                @(surveyQuestionsNamND.IsActive ? "Active" : "Inactive")
                            </span>
                        </td>
                        <td>
                            <div class="date-text">@surveyQuestionsNamND.CreatedDate?.ToString("dd/MM/yyyy")</div>
                        </td>
                        <td>
                            <span class="question-type">@surveyQuestionsNamND.QuestionType</span>
                        </td>
                        <td>
                            <span class="status-badge @(surveyQuestionsNamND.IsRequired ? "required-yes" : "required-no")">
                                @(surveyQuestionsNamND.IsRequired ? "Yes" : "No")
                            </span>
                        </td>
                        <td style="text-align: left;">
                            <div class="options-text">
                                @if (!string.IsNullOrEmpty(surveyQuestionsNamND.Options))
                                {
                                    @surveyQuestionsNamND.Options
                                }
                                else
                                {
                                    <span class="text-muted">-</span>
                                }
                            </div>
                        </td>
                        <td>
                            <span class="risk-weight">@surveyQuestionsNamND.RiskWeight</span>
                        </td>
                        <td style="text-align: left;">
                            <div class="survey-name">
                                @if (!string.IsNullOrEmpty(surveyQuestionsNamND.SurveyNamND?.SurveyName))
                                {
                                    @surveyQuestionsNamND.SurveyNamND.SurveyName
                                }
                                else
                                {
                                    <span class="text-muted">-</span>
                                }
                            </div>
                        </td>
                        <td>
                            <div class="d-flex gap-2 justify-content-center">
                                <a href="/SurveyQuestionsNamNDs/SurveyQuestionsNamNDDetail/@surveyQuestionsNamND.QuestionNamNDID"
                                   class="btn-modern btn-detail">
                                    Detail
                                </a>
                                <a href="/SurveyQuestionsNamNDs/SurveyQuestionsNamNDForm/@surveyQuestionsNamND.QuestionNamNDID"
                                   class="btn-modern btn-edit">
                                    Edit
                                </a>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>

        <!-- Phân trang -->
        <div class="page-info">
            Showing @((currentPage - 1) * pageSize + 1) to @Math.Min(currentPage * pageSize, surveyQuestionsNamNDs?.Count ?? 0) of @(surveyQuestionsNamNDs?.Count ?? 0) entries
        </div>

        <div class="pagination">
            <button class="pagination-item pagination-nav" @onclick="PreviousPage" disabled="@(currentPage == 1)">
                Previous
            </button>
            
            @{
                int startPage = Math.Max(1, currentPage - 2);
                int endPage = Math.Min(totalPages, startPage + 4);
                
                if (endPage - startPage < 4 && totalPages > 4)
                {
                    startPage = Math.Max(1, endPage - 4);
                }
            }
            
            @if (startPage > 1)
            {
                <button class="pagination-item pagination-number" @onclick="() => GoToPage(1)">1</button>
                @if (startPage > 2)
                {
                    <span class="pagination-item">...</span>
                }
            }
            
            @for (int i = startPage; i <= endPage; i++)
            {
                int pageNumber = i;
                <button class="pagination-item @(pageNumber == currentPage ? "pagination-active" : "pagination-number")" 
                        @onclick="() => GoToPage(pageNumber)">
                    @pageNumber
                </button>
            }
            
            @if (endPage < totalPages)
            {
                @if (endPage < totalPages - 1)
                {
                    <span class="pagination-item">...</span>
                }
                <button class="pagination-item pagination-number" @onclick="() => GoToPage(totalPages)">@totalPages</button>
            }
            
            <button class="pagination-item pagination-nav" @onclick="NextPage" disabled="@(currentPage == totalPages || totalPages == 0)">
                Next
            </button>
        </div>
    </div>
}

@code {
    private List<SurveyQuestionsNamND>? surveyQuestionsNamNDs;
    private List<SurveyQuestionsNamND>? allSurveyQuestionsNamNDs; // Lưu trữ tất cả câu hỏi để reset tìm kiếm
    private List<SurveysNamND>? surveys; // Danh sách surveys cho dropdown
    
    // Biến tìm kiếm
    private int searchQuestionId;
    private string searchQuestionText = "";
    private int searchSurveyId; // Thay đổi từ string sang int để lưu ID
    
    // Biến phân trang
    private int currentPage = 1;
    private int pageSize = 5;
    private int totalPages => allSurveyQuestionsNamNDs != null ? (int)Math.Ceiling(allSurveyQuestionsNamNDs.Count / (double)pageSize) : 0;
    private List<SurveyQuestionsNamND> pagedQuestions => surveyQuestionsNamNDs != null 
        ? surveyQuestionsNamNDs.Skip((currentPage - 1) * pageSize).Take(pageSize).ToList() 
        : new List<SurveyQuestionsNamND>();

    protected override async Task OnInitializedAsync()
    {
        await Task.Delay(500);
        // Lấy danh sách surveys
        surveys = await _serviceProviders.SurveysNamNDService.GetAllAsync();
        
        // Lấy danh sách câu hỏi
        allSurveyQuestionsNamNDs = await _serviceProviders.SurveyQuestionsNamNDService.GetAllAsync();
        surveyQuestionsNamNDs = allSurveyQuestionsNamNDs;
    }

    private async Task SearchQuestions()
    {
        if (searchSurveyId == 0 && string.IsNullOrWhiteSpace(searchQuestionText) && searchQuestionId == 0)
        {
            // Nếu không có điều kiện tìm kiếm, hiển thị tất cả
            surveyQuestionsNamNDs = allSurveyQuestionsNamNDs;
            return;
        }

        // Tìm kiếm dựa trên các điều kiện
        surveyQuestionsNamNDs = allSurveyQuestionsNamNDs.Where(q => 
            (searchQuestionId == 0 || q.QuestionNamNDID == searchQuestionId) &&
            (string.IsNullOrWhiteSpace(searchQuestionText) || q.QuestionText.Contains(searchQuestionText, StringComparison.OrdinalIgnoreCase)) &&
            (searchSurveyId == 0 || q.SurveyNamNDID == searchSurveyId)
        ).ToList();
    }

    private void ResetSearch()
    {
        searchQuestionId = 0;
        searchQuestionText = "";
        searchSurveyId = 0;
        surveyQuestionsNamNDs = allSurveyQuestionsNamNDs;
    }

    private void GoToPage(int page)
    {
        if (page < 1)
            currentPage = 1;
        else if (page > totalPages)
            currentPage = totalPages;
        else
            currentPage = page;
    }

    private void NextPage()
    {
        if (currentPage < totalPages)
            currentPage++;
    }

    private void PreviousPage()
    {
        if (currentPage > 1)
            currentPage--;
    }
}
