﻿@page "/"
@attribute [Authorize]

<PageTitle>Drug Prevention System - Home</PageTitle>

<div class="page-header">
    <div class="container">
        <h1><i class="fas fa-shield-alt"></i> Drug Prevention System</h1>
        <p class="lead">Welcome to the comprehensive drug prevention management system</p>
    </div>
</div>

<div class="container">
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-question-circle text-primary"></i> Survey Questions</h5>
                    <p class="card-text">Manage and view survey questions for drug prevention research.</p>
                    <a href="/SurveyQuestionsNamNDs" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i> View Questions
                    </a>
                </div>
            </div>
        </div>

        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title"><i class="fas fa-chart-bar text-success"></i> Analytics</h5>
                    <p class="card-text">View comprehensive analytics and reports on drug prevention surveys.</p>
                    <a href="#" class="btn btn-success">
                        <i class="fas fa-chart-line"></i> View Analytics
                    </a>
                </div>
            </div>
        </div>
    </div>

    <AuthorizeView>
        <Authorized>
            <div class="alert alert-info">
                <h4><i class="fas fa-user"></i> Welcome, @context.User.Identity?.Name!</h4>
                <p>You are successfully logged into the Drug Prevention System.</p>
            </div>
        </Authorized>
    </AuthorizeView>
</div>

<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .card {
        border: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .card:hover {
        transform: translateY(-5px);
    }

    .card-title i {
        margin-right: 0.5rem;
    }

    .btn i {
        margin-right: 0.5rem;
    }
</style>
