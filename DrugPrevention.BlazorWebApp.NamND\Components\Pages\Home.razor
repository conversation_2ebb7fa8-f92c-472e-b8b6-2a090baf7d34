﻿@page "/Home"
@rendermode InteractiveServer
@using DrugPrevention.BlazorWebApp.NamND.Services
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation

<PageTitle>Drug Prevention System</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
                <h1 class="display-4">🛡️ Drug Prevention System</h1>
                <p class="lead">Welcome to the comprehensive drug prevention management system.</p>
                <hr class="my-4">
                <p>Manage surveys, track progress, and help build a drug-free community.</p>
                <a class="btn btn-light btn-lg" href="/SurveyQuestionsNamNDs/SurveyQuestionsNamNDList" role="button">
                    <i class="bi bi-list-ul"></i> View Survey Questions
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-question-circle text-primary"></i> Survey Management
                    </h5>
                    <p class="card-text">Create and manage survey questions to gather important data about drug prevention efforts.</p>
                    <a href="/SurveyQuestionsNamNDs/SurveyQuestionsNamNDList" class="btn btn-primary">Manage Surveys</a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-graph-up text-success"></i> Analytics
                    </h5>
                    <p class="card-text">View comprehensive analytics and reports on drug prevention program effectiveness.</p>
                    <a href="#" class="btn btn-success">View Analytics</a>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="bi bi-people text-info"></i> Community
                    </h5>
                    <p class="card-text">Connect with community members and track engagement in prevention programs.</p>
                    <a href="#" class="btn btn-info">Community Hub</a>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        var isAuthenticated = await AuthService.IsAuthenticatedAsync();
        if (!isAuthenticated)
        {
            Navigation.NavigateTo("/Account/Login", true);
        }
    }
}
