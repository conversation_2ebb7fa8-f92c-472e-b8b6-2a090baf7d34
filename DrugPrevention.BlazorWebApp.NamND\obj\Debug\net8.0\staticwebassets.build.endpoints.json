{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "DrugPrevention.BlazorWebApp.NamND.qa7n8yemvd.styles.css", "AssetFile": "DrugPrevention.BlazorWebApp.NamND.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "7673"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TkspOBcVsIOIJEIV3y1IcvEWFLpuQMxrf4tIHOQ+EL4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 11:17:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qa7n8yemvd"}, {"Name": "integrity", "Value": "sha256-TkspOBcVsIOIJEIV3y1IcvEWFLpuQMxrf4tIHOQ+EL4="}, {"Name": "label", "Value": "DrugPrevention.BlazorWebApp.NamND.styles.css"}]}, {"Route": "DrugPrevention.BlazorWebApp.NamND.styles.css", "AssetFile": "DrugPrevention.BlazorWebApp.NamND.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "7673"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TkspOBcVsIOIJEIV3y1IcvEWFLpuQMxrf4tIHOQ+EL4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 11:17:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TkspOBcVsIOIJEIV3y1IcvEWFLpuQMxrf4tIHOQ+EL4="}]}, {"Route": "app.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}]}, {"Route": "app.da95v2qkru.css", "AssetFile": "app.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "2591"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "da95v2qkru"}, {"Name": "integrity", "Value": "sha256-u9qEka1auR7E3rd3/8/j8hkQdSOYj9bRJ4nYiFDR1sE="}, {"Name": "label", "Value": "app.css"}]}, {"Route": "bootstrap/bootstrap.min.bpk8xqwxhs.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bpk8xqwxhs"}, {"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css"}]}, {"Route": "bootstrap/bootstrap.min.css", "AssetFile": "bootstrap/bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "162720"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg="}]}, {"Route": "bootstrap/bootstrap.min.css.8inm30yfxf.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8inm30yfxf"}, {"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}, {"Name": "label", "Value": "bootstrap/bootstrap.min.css.map"}]}, {"Route": "bootstrap/bootstrap.min.css.map", "AssetFile": "bootstrap/bootstrap.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "449111"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ="}]}, {"Route": "favicon.ifv42okdf2.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ifv42okdf2"}, {"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}, {"Name": "label", "Value": "favicon.png"}]}, {"Route": "favicon.png", "AssetFile": "favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "1148"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 08:52:06 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4mWsDy3aHl36ZbGt8zByK7Pvd4kRUoNgTYzRnwmPHwg="}]}, {"Route": "hamster.a0vl44ky7a.gif", "AssetFile": "hamster.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "60556"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"OmShwrq3kntKhEPDLf3xFP7/oF+D5jg90oJ6BsbkmNU=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 19:26:55 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "a0vl44ky7a"}, {"Name": "integrity", "Value": "sha256-OmShwrq3kntKhEPDLf3xFP7/oF+D5jg90oJ6BsbkmNU="}, {"Name": "label", "Value": "hamster.gif"}]}, {"Route": "hamster.gif", "AssetFile": "hamster.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "60556"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"OmShwrq3kntKhEPDLf3xFP7/oF+D5jg90oJ6BsbkmNU=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 19:26:55 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OmShwrq3kntKhEPDLf3xFP7/oF+D5jg90oJ6BsbkmNU="}]}, {"Route": "pacman.aug6tgiv90.gif", "AssetFile": "pacman.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "54276"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"PgjYJ/iGYa+qTpVeVtpyNDqBwvQeoq86zZ8Ppjga4fY=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 19:34:28 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aug6tgiv90"}, {"Name": "integrity", "Value": "sha256-PgjYJ/iGYa+qTpVeVtpyNDqBwvQeoq86zZ8Ppjga4fY="}, {"Name": "label", "Value": "pacman.gif"}]}, {"Route": "pacman.gif", "AssetFile": "pacman.gif", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "54276"}, {"Name": "Content-Type", "Value": "image/gif"}, {"Name": "ETag", "Value": "\"PgjYJ/iGYa+qTpVeVtpyNDqBwvQeoq86zZ8Ppjga4fY=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 19:34:28 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PgjYJ/iGYa+qTpVeVtpyNDqBwvQeoq86zZ8Ppjga4fY="}]}]}